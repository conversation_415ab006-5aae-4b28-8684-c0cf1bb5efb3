# 保險系統 SignPDF - 系統需求分析文檔

## 1. 系統概覽

### 1.1 系統名稱
**SignPDF 保險業務自動化處理系統**

### 1.2 系統目的
本系統是一個基於 ASP.NET Core 3.1 的保險業務自動化處理系統，主要用於：
- PDF 文件的自動簽名和處理
- 保險業務流程的自動化管理
- 與第三方系統（如 A8 協同辦公系統）的集成
- 保險相關報表和文件的生成與分發

### 1.3 技術架構
- **框架**: ASP.NET Core 3.1 MVC
- **開發語言**: C#
- **PDF處理**: iText7, iTextSharp
- **數據庫**: SQL Server
- **日誌**: NLog
- **自動化**: Selenium WebDriver
- **郵件**: System.Net.Mail
- **JSON處理**: Newtonsoft.Json, Nancy.Json

## 2. 系統架構分析

### 2.1 項目結構
```
SignPDF/
├── Controllers/          # MVC控制器
├── Models/              # 數據模型
├── Views/               # 視圖文件
├── DTO/                 # 數據傳輸對象
├── API/                 # API工具類
├── PDF/                 # PDF處理相關
├── Properties/          # 項目配置
├── wwwroot/            # 靜態資源
└── 配置文件
```

### 2.2 核心組件

#### 2.2.1 主控制器 (HomeController)
- **路徑**: `SignPDF/Controllers/HomeController.cs`
- **功能**: 
  - 系統主入口點
  - 定時任務調度
  - 業務流程協調

#### 2.2.2 核心業務邏輯 (ScanCheck)
- **路徑**: `SignPDF/DTO/ScanCheck.cs`
- **功能**: 
  - 保險業務數據掃描和檢查
  - 與外部系統集成
  - 自動化流程處理

#### 2.2.3 PDF處理模塊
- **路徑**: `SignPDF/PDF/`
- **功能**:
  - PDF文件簽名
  - PDF關鍵字查找和定位
  - PDF圖像插入

## 3. 核心功能需求

### 3.1 PDF處理功能

#### 3.1.1 PDF簽名功能
- **需求描述**: 自動在PDF文件中插入簽名圖像
- **實現方式**: 
  - 使用關鍵字定位簽名位置
  - 支持多種簽名位置標識（"Authorised Signature", "________________________________________"）
  - 自動調整簽名圖像大小和位置

#### 3.1.2 PDF關鍵字查找
- **需求描述**: 在PDF文件中查找特定關鍵字並獲取位置坐標
- **技術實現**: 使用iTextSharp進行PDF內容解析

### 3.2 業務流程自動化

#### 3.2.1 定時任務系統
- **需求描述**: 系統需要支持定時執行各種業務檢查和處理任務
- **執行頻率**: 每10分鐘執行一次
- **主要任務**:
  - Bond() - 債券數據處理（工作日10點執行）
  - Check() - 保單檢查和PDF處理
  - InsertECO() - ECO通知插入
  - ECO() - ECO郵件發送
  - Cheque() - 支票處理
  - State() - 狀態更新
  - CDMSExpenseCheck() - CDMS費用檢查
  - CDMSReceiptCheck() - CDMS收據檢查

#### 3.2.2 特定時間任務
- **每日9點執行**:
  - PayslipAlertA8() - 薪資單提醒
  - CertAlertA8() - 證書提醒
- **每月1號執行**:
  - ClosingClaims() - 理賠結案
  - Updateflogseq() - 流水號更新

### 3.3 外部系統集成

#### 3.3.1 A8協同辦公系統集成
- **API端點**: `https://i.3311csci.com/seeyon/rest/`
- **功能**:
  - 流程發起
  - 文件上傳
  - 數據同步
- **認證方式**: Token認證

#### 3.3.2 數據庫集成
- **主數據庫**: 保險業務數據庫
- **CDMS數據庫**: `Data Source=**********;User ID=SSIS;PassWord=*********;Initial Catalog=CSCI_DATASOURCE`
- **ECO數據庫**: 工傷管理數據庫

### 3.4 郵件系統

#### 3.4.1 客戶郵件發送
- **功能**: 向客戶發送電子保單
- **郵件服務器**: `**********:587`
- **發送賬戶**: `<EMAIL>`

#### 3.4.2 內部通知郵件
- **功能**: 工傷案件提醒郵件
- **郵件服務器**: `owahk.cohl.com:25`

## 4. 數據結構分析

### 4.1 核心數據傳輸對象 (DTO)

#### 4.1.1 JsonOutput
- **用途**: 標準API響應格式
- **屬性**:
  - Code: 響應代碼
  - Message: 響應消息
  - Result: 響應結果

#### 4.1.2 BJasonOutPut
- **用途**: 業務流程數據輸出
- **結構**: 包含RowData和DataMap的複雜對象

#### 4.1.3 SecurityOutPut, QJasonOutPut, PJasonOutPut
- **用途**: 不同業務場景的數據輸出格式
- **特點**: 都包含類似的RowData結構但字段略有不同

#### 4.1.4 JasonOutputAttach
- **用途**: 附件信息輸出
- **屬性**: 包含文件ID、名稱、大小、類型等信息

### 4.2 業務數據模型

#### 4.2.1 保單數據 (polh)
- **主要字段**:
  - fpolno: 保單號
  - fclass: 保險類別
  - fctldel: 控制刪除標記
  - finsd: 被保險人

#### 4.2.2 理賠數據
- **相關表**: 多個理賠相關表
- **功能**: 理賠案件管理和處理

#### 4.2.3 工傷數據 (T_Report_O)
- **功能**: 工傷案件管理
- **關聯**: 與ECO系統集成

## 5. 系統配置需求

### 5.1 應用程序配置
- **部署URL**: `http://pur.csci.com.hk:92/SignPDF`
- **開發環境**: `http://localhost:54382`
- **日誌配置**: 使用NLog進行日誌記錄

### 5.2 文件路徑配置
- **PDF存儲路徑**: `\\*********\保險公司\COIL_DATA\`
- **簽名圖像**: `zhou.png`
- **報表輸出**: 多個子目錄用於不同類型文件

### 5.3 數據庫連接
- **主數據庫**: 保險核心業務數據庫
- **CDMS數據庫**: 建築項目管理數據庫
- **ECO數據庫**: 工傷管理數據庫

## 6. 安全需求

### 6.1 認證授權
- **A8系統**: 基於Token的認證
- **數據庫**: SQL Server認證
- **文件訪問**: 網絡共享文件夾權限

### 6.2 數據安全
- **敏感數據**: 保險客戶信息加密存儲
- **日誌記錄**: 操作審計跟蹤
- **錯誤處理**: 異常信息不暴露敏感數據

## 7. 性能需求

### 7.1 響應時間
- **PDF處理**: 單個文件處理時間 < 30秒
- **數據庫查詢**: 一般查詢 < 5秒
- **API調用**: 外部API調用 < 60秒

### 7.2 併發處理
- **定時任務**: 避免重複執行
- **文件處理**: 支持批量處理
- **數據庫連接**: 連接池管理

## 8. 運維需求

### 8.1 監控需求
- **日誌監控**: NLog日誌文件監控
- **系統狀態**: 定時任務執行狀態
- **錯誤報警**: 關鍵錯誤自動報警

### 8.2 備份需求
- **數據備份**: 定期數據庫備份
- **文件備份**: PDF文件定期備份
- **配置備份**: 系統配置文件備份

## 9. 詳細業務流程分析

### 9.1 PDF處理流程
1. **文件掃描**: 掃描指定目錄下的PDF文件
2. **狀態檢查**: 檢查文件處理狀態（Finished但未Send）
3. **關鍵字定位**: 在PDF中查找簽名位置關鍵字
4. **簽名插入**: 在指定位置插入簽名圖像
5. **文件保存**: 將處理後的文件保存到目標目錄
6. **狀態更新**: 更新文件處理狀態為已發送

### 9.2 工傷案件處理流程
1. **數據掃描**: 掃描ECO數據庫中的工傷案件
2. **時間檢查**: 檢查案件發生日期是否超過75天
3. **郵件發送**: 向相關人員發送提醒郵件
4. **狀態記錄**: 記錄郵件發送狀態和時間
5. **A8流程**: 自動發起A8協同辦公流程

### 9.3 A8系統集成流程
1. **Token獲取**: 通過用戶ID獲取訪問令牌
2. **數據準備**: 準備流程發起所需的JSON數據
3. **流程發起**: 調用A8 API發起業務流程
4. **結果處理**: 解析API響應並提取流程ID
5. **狀態同步**: 將流程狀態同步到本地數據庫

### 9.4 郵件發送流程
1. **收件人確定**: 根據業務規則確定郵件收件人
2. **內容生成**: 根據模板生成郵件內容
3. **附件處理**: 添加相關PDF文件作為附件
4. **郵件發送**: 通過SMTP服務器發送郵件
5. **結果記錄**: 記錄發送結果和時間

## 10. 技術規格詳細說明

### 10.1 依賴包分析
- **itext7 (7.1.12)**: 現代PDF處理庫，用於新的PDF操作
- **iTextSharp (5.5.13.1)**: 傳統PDF處理庫，用於兼容性
- **Microsoft.Office.Interop.Word (15.0.4797.1003)**: Word文檔處理
- **Nancy (2.0.0)**: 輕量級Web框架，用於JSON序列化
- **NLog (4.7.4)**: 日誌記錄框架
- **Selenium.WebDriver (3.141.0)**: Web自動化測試工具
- **System.Data.SqlClient (4.8.2)**: SQL Server數據訪問

### 10.2 API接口規格

#### 10.2.1 A8系統API
- **基礎URL**: `https://i.3311csci.com/seeyon/rest/`
- **認證方式**: Bearer Token
- **主要端點**:
  - `/token/common-rest-user/{app-id}?loginName={userId}`: 獲取用戶令牌
  - `/bpm/process/start`: 發起業務流程
  - `/flow/FromFinish/{type}/{dateFrom}/{dateTo}`: 獲取已完成流程

#### 10.2.2 內部API
- **Test端點**: `/Home/Test?id={id}` - 測試接口
- **主頁**: `/Home/Index` - 主要業務處理入口

### 10.3 數據庫設計分析

#### 10.3.1 主要數據表
- **polh**: 保單主表
- **cover_note**: 暫保單表
- **reportrecord**: 報表記錄表
- **cheque**: 支票表
- **ostat2**: 狀態表
- **CAP4FormApprove**: A8表單審批表
- **T_Report_O**: 工傷報告表
- **Notification**: 通知表

#### 10.3.2 關鍵字段說明
- **fctldel**: 控制刪除標記，用於標識處理狀態
- **fpolno**: 保單號，業務主鍵
- **flowid**: 流程ID，與A8系統關聯
- **BillStatus**: 賬單狀態
- **SummaryId**: 摘要ID，用於流程跟蹤

## 11. 錯誤處理和異常管理

### 11.1 異常類型
- **PDF處理異常**: 文件損壞、格式不支持
- **數據庫連接異常**: 連接超時、權限問題
- **API調用異常**: 網絡錯誤、認證失敗
- **文件系統異常**: 路徑不存在、權限不足

### 11.2 錯誤處理策略
- **重試機制**: 對於臨時性錯誤實施重試
- **降級處理**: 關鍵功能失敗時的備用方案
- **錯誤記錄**: 詳細記錄錯誤信息用於調試
- **用戶通知**: 適當的錯誤信息反饋

## 12. 擴展需求

### 12.1 功能擴展
- **新業務類型**: 支持新的保險產品類型
- **新集成系統**: 與其他外部系統集成
- **移動端支持**: 移動設備訪問支持
- **多語言支持**: 支持繁體中文、簡體中文、英文

### 12.2 技術升級
- **.NET版本升級**: 從.NET Core 3.1升級到.NET 6/7/8
- **數據庫優化**: 查詢性能優化和索引優化
- **雲端部署**: 支持Azure、AWS等雲端部署
- **容器化**: Docker容器化部署支持

### 12.3 安全增強
- **身份認證**: 集成企業級身份認證系統
- **數據加密**: 敏感數據端到端加密
- **審計日誌**: 完整的操作審計跟蹤
- **權限管理**: 細粒度的權限控制系統
