# PDF簽名系統企業級需求文檔

## 文檔信息
- **項目名稱**: 中國海外保險PDF數位簽名系統
- **版本**: 1.0
- **創建日期**: 2025-01-08
- **文檔類型**: 企業級需求規格說明書

## 1. 系統概述

### 1.1 項目背景
中國海外保險PDF數位簽名系統是一個基於ASP.NET Core 3.1開發的自動化保險文檔處理系統。該系統主要用於自動化處理保險單據的數位簽名，包括保單、理賠文件、業務報告等各類PDF文檔的簽名和分發。

### 1.2 業務目標
- 自動化PDF文檔簽名流程，提高工作效率
- 確保保險文檔的合規性和法律效力
- 實現文檔的自動分發和歸檔
- 減少人工操作錯誤，提升文檔處理質量

### 1.3 系統範圍
系統涵蓋以下業務領域：
- 保險單據自動簽名（PMP、CMP等車險保單）
- 理賠文件處理和簽名
- 業務報告自動生成和簽名
- 到期通知書處理
- 客戶郵件自動發送

## 2. 系統架構

### 2.1 技術架構
- **前端框架**: ASP.NET Core MVC 3.1
- **後端語言**: C# .NET Core 3.1
- **數據庫**: SQL Server
- **PDF處理**: iTextSharp 5.5.13.1 + iText7 7.1.12
- **Web自動化**: Selenium WebDriver 3.141.0
- **日誌框架**: NLog 4.7.4
- **郵件服務**: System.Net.Mail

### 2.2 部署架構
- **應用服務器**: Windows Server環境
- **數據庫服務器**: SQL Server (COB-SERVER-090, COB-SERVER-188)
- **文件服務器**: 網絡共享存儲 (\\10.1.8.27\保險公司\)
- **郵件服務器**: 企業郵件服務器 (**********)

## 3. 功能需求

### 3.1 核心功能模組

#### 3.1.1 PDF簽名處理模組
**功能描述**: 自動識別PDF文檔中的簽名位置並插入數位簽名圖片

**主要功能**:
- PDF文檔解析和文本提取
- 關鍵字定位和坐標計算
- 簽名圖片插入和位置調整
- 多頁面文檔處理
- 簽名後文檔生成

**支持的簽名類型**:
- 授權簽名 (Authorised Signature)
- 審批簽名 (Approved by: Zhou Jin Xing)
- 檢查簽名 (Checked by: April Lim)
- 準備簽名 (Prepared by: Arthur Chang)
- 理賠部門簽名 (Checked by: Claims Department)

#### 3.1.2 文檔掃描和監控模組
**功能描述**: 定時掃描數據庫和文件系統，識別需要處理的文檔

**主要功能**:
- 數據庫狀態監控
- 文件系統掃描
- 處理隊列管理
- 錯誤重試機制
- 處理狀態追蹤

#### 3.1.3 郵件發送模組
**功能描述**: 自動發送簽名後的文檔給客戶和相關人員

**主要功能**:
- 客戶郵件自動發送
- 附件管理
- 郵件模板管理
- 發送狀態追蹤
- 失敗重發機制

#### 3.1.4 業務報告生成模組
**功能描述**: 生成各類業務報告並進行簽名處理

**主要功能**:
- 業務報告自動生成
- 理賠報告處理
- 統計報表生成
- HTML轉PDF功能
- 報告簽名和分發

### 3.2 輔助功能模組

#### 3.2.1 系統監控模組
- 定時任務管理
- 系統健康檢查
- 性能監控
- 錯誤日誌記錄

#### 3.2.2 外部系統集成
- A8系統API集成
- 第三方服務調用
- 數據同步功能
- 令牌管理

## 4. 數據結構分析

### 4.1 核心數據表

#### 4.1.1 保單主表 (polh)
```sql
主要字段:
- fpolno: 保單號
- fclass: 保險類別
- fctldel: 處理狀態標識
- fconfirm: 確認狀態
- fcnfdate: 確認日期
- fendtno: 批單號
- finsd: 被保險人
- fgpm: 毛保費
```

#### 4.1.2 理賠表 (claims相關表)
```sql
主要字段:
- fclmno: 理賠號
- flosdate: 出險日期
- famount: 理賠金額
- fstatus: 理賠狀態
```

#### 4.1.3 客戶表 (client相關表)
```sql
主要字段:
- fclnt: 客戶代碼
- fdesc: 客戶描述
- finter: 是否集團內部
- fsubside: 子公司標識
```

### 4.2 配置數據

#### 4.2.1 簽名關鍵字配置
系統預定義了多種簽名關鍵字，用於定位簽名位置：
- "Authorised Signature"
- "Approved by: Zhou Jin Xing"
- "Checked by: April Lim"
- "Prepared by: Arthur Chang"
- "FACULTATIVE"
- "CHINA OVERSEAS INSURANCE LIMITED"

#### 4.2.2 文件路徑配置
```
輸入路徑: \\10.1.8.27\保險公司\COIL_DATA\PushToA8\
輸出路徑: \\10.1.8.27\保險公司\COIL_DATA\COIL_ePolicy\
備份路徑: \\10.1.8.27\保險公司\COIL_DATA\COIL_ePolicyCopy\
發送路徑: \\10.1.8.27\保險公司\COIL_DATA\SendToCustomer\
```

## 5. 非功能性需求

### 5.1 性能需求
- **處理能力**: 系統應能每小時處理至少100個PDF文檔
- **響應時間**: 單個PDF簽名處理時間不超過30秒
- **並發處理**: 支持同時處理多個文檔（通過定時任務隊列）
- **文件大小**: 支持最大50MB的PDF文檔處理

### 5.2 可靠性需求
- **系統可用性**: 99.5%以上的系統可用性
- **錯誤恢復**: 具備自動錯誤恢復和重試機制
- **數據完整性**: 確保文檔處理過程中數據不丟失
- **備份機制**: 自動備份處理後的文檔

### 5.3 安全性需求
- **訪問控制**: 基於角色的訪問控制
- **數據加密**: 敏感數據傳輸加密
- **審計日誌**: 完整的操作審計日誌
- **文檔安全**: 簽名文檔的完整性保護

### 5.4 可維護性需求
- **日誌記錄**: 詳細的系統運行日誌
- **錯誤追蹤**: 完善的錯誤追蹤和報告機制
- **配置管理**: 靈活的配置參數管理
- **監控告警**: 系統異常自動告警

### 5.5 可擴展性需求
- **模組化設計**: 支持功能模組的獨立擴展
- **API接口**: 提供標準API接口供外部系統調用
- **多格式支持**: 未來可擴展支持其他文檔格式
- **多簽名支持**: 支持多種簽名類型和樣式

## 6. 技術棧和依賴項

### 6.1 核心依賴包
```xml
<PackageReference Include="itext7" Version="7.1.12" />
<PackageReference Include="iTextSharp" Version="5.5.13.1" />
<PackageReference Include="Microsoft.Office.Interop.Word" Version="15.0.4797.1003" />
<PackageReference Include="Nancy" Version="2.0.0" />
<PackageReference Include="NLog" Version="4.7.4" />
<PackageReference Include="NLog.Web.AspNetCore" Version="4.7.0" />
<PackageReference Include="Selenium.Support" Version="3.141.0" />
<PackageReference Include="Selenium.WebDriver" Version="3.141.0" />
<PackageReference Include="System.Data.SqlClient" Version="4.8.2" />
```

### 6.2 外部工具依賴
- **Chrome瀏覽器**: 用於Web自動化和PDF生成
- **wkhtmltopdf**: HTML轉PDF工具
- **Microsoft Word**: 文檔處理支持

## 7. 部署和配置要求

### 7.1 服務器要求
- **操作系統**: Windows Server 2016或更高版本
- **內存**: 最低8GB RAM，推薦16GB
- **存儲**: 最低100GB可用空間
- **網絡**: 穩定的內網連接

### 7.2 數據庫要求
- **數據庫**: SQL Server 2014或更高版本
- **連接字符串**: 配置兩個數據庫連接
  - 主數據庫: COB-SERVER-090
  - 輔助數據庫: COB-SERVER-188

### 7.3 網絡配置
- **文件共享**: 配置網絡文件共享訪問權限
- **郵件服務**: 配置SMTP服務器連接
- **防火牆**: 開放必要的端口和服務

## 8. 測試策略

### 8.1 單元測試
- PDF處理功能測試
- 簽名定位算法測試
- 數據庫操作測試
- 郵件發送功能測試

### 8.2 集成測試
- 端到端業務流程測試
- 外部系統集成測試
- 文件系統操作測試
- 錯誤處理流程測試

### 8.3 性能測試
- 大批量文檔處理測試
- 並發處理能力測試
- 內存使用情況測試
- 長時間運行穩定性測試

### 8.4 安全測試
- 訪問權限測試
- 數據安全性測試
- 文檔完整性測試
- 系統漏洞掃描

## 9. 風險評估和緩解策略

### 9.1 技術風險
- **PDF格式兼容性**: 建立PDF格式測試庫，確保兼容性
- **第三方庫依賴**: 定期更新依賴包，建立備用方案
- **性能瓶頸**: 實施性能監控，優化關鍵算法

### 9.2 業務風險
- **文檔處理失敗**: 建立完善的錯誤處理和重試機制
- **簽名位置錯誤**: 實施簽名位置驗證和人工審核流程
- **數據丟失**: 建立多重備份和恢復機制

### 9.3 運維風險
- **系統故障**: 建立監控告警和快速恢復機制
- **數據庫連接問題**: 實施連接池和故障轉移
- **網絡中斷**: 建立離線處理和同步機制

## 10. 項目實施計劃

### 10.1 開發階段
1. **需求分析和設計** (已完成)
2. **核心功能開發** (已完成)
3. **集成測試** (建議進行)
4. **性能優化** (建議進行)

### 10.2 部署階段
1. **測試環境部署**
2. **用戶接受測試**
3. **生產環境部署**
4. **系統上線**

### 10.3 維護階段
1. **系統監控**
2. **定期維護**
3. **功能擴展**
4. **技術升級**

## 11. 詳細技術規格

### 11.1 PDF處理技術細節

#### 11.1.1 關鍵字定位算法
系統使用自定義的關鍵字定位算法，具體實現包括：

**核心類**: `PdfKeywordFinder`
- **功能**: 在PDF文檔中精確定位簽名關鍵字位置
- **算法**: 基於字符位置計算的精確定位
- **返回值**: 頁碼、X坐標、Y坐標、字符寬度

**支持的關鍵字類型**:
```csharp
// 授權簽名
"Authorised Signature"
// 下劃線簽名位置
"________________________________________"
// 法律條款位置
"(CHAPTER 272)", "THE SCHEDULE", "SCHEDULE", "ENDORSEMENT"
// 到期通知
"EXPIRY NOTICE"
// 審批簽名
"Approved by: Zhou Jin Xing"
"Checked by: April Lim"
"Prepared by: Arthur Chang"
"Checked by: Claims Department"
// 再保險相關
"FACULTATIVE", "Reinsurance confirmed and accepted"
// 公司標識
"CHINA OVERSEAS INSURANCE LIMITED"
```

#### 11.1.2 簽名圖片處理
**圖片資源管理**:
- 簽名圖片存放路徑: `\\10.1.8.27\保險公司\COIL_DATA\PushToA8\`
- 支持的圖片格式: PNG
- 主要簽名圖片:
  - `header.png`: 公司頭部標識 (577x37像素)
  - `zhou.png`: 周總簽名 (80x50像素)
  - `Leanne.png`: Leanne簽名 (80x50像素)

**圖片插入技術**:
- 使用iTextSharp的`PdfStamper`類進行圖片插入
- 支持圖片縮放和位置調整
- 自動計算最佳插入位置

#### 11.1.3 PDF文檔類型支持
系統支持處理多種類型的PDF文檔：

1. **保險單據**:
   - 車險保單 (PMP/CMP)
   - 一般保險保單
   - 批單和附加條款

2. **理賠文件**:
   - 理賠報告
   - 理賠審批表
   - 理賠統計報表

3. **業務報告**:
   - 月度業務報告
   - 年度統計報表
   - 監管報告

4. **通知文件**:
   - 到期通知書
   - 續保提醒
   - 客戶通知

### 11.2 數據庫設計詳情

#### 11.2.1 主要數據表結構

**保單主表 (polh)**:
```sql
CREATE TABLE polh (
    fctlid NVARCHAR(50) PRIMARY KEY,    -- 控制ID
    fpolno NVARCHAR(50),                -- 保單號
    fendtno NVARCHAR(50),               -- 批單號
    fclass NVARCHAR(10),                -- 保險類別
    fsclass NVARCHAR(10),               -- 子類別
    fbus NVARCHAR(5),                   -- 業務類型
    fconfirm INT,                       -- 確認狀態
    fcnfdate DATETIME,                  -- 確認日期
    fctldel NVARCHAR(255),              -- 處理狀態
    finsd NVARCHAR(255),                -- 被保險人
    fgpm DECIMAL(18,2),                 -- 毛保費
    fnpm DECIMAL(18,2),                 -- 淨保費
    fissdate DATETIME,                  -- 簽發日期
    fincfr DATETIME,                    -- 保險起期
    fincto DATETIME,                    -- 保險止期
    fclnt NVARCHAR(50),                 -- 客戶代碼
    fprdr NVARCHAR(50)                  -- 生產者代碼
);
```

**處理狀態表 (ostat2)**:
```sql
CREATE TABLE ostat2 (
    refno1 NVARCHAR(50),                -- 參考號1
    摘要 NVARCHAR(255),                  -- 摘要
    制单人 NVARCHAR(50),                 -- 制單人
    日期 DATETIME                        -- 日期
);
```

#### 11.2.2 數據庫連接配置
系統使用兩個數據庫連接：

**主數據庫連接**:
```
Server: COB-SERVER-090
Database: live_ilodata
User: common
Password: gn9gnahc14
Timeout: 400秒
```

**輔助數據庫連接**:
```
Server: COB-SERVER-188
Database: CSCI_DATASOURCE
User: SSIS
Password: Csci@3311
Timeout: 400秒
```

### 11.3 定時任務機制

#### 11.3.1 TimedHostedService實現
系統使用ASP.NET Core的`IHostedService`實現定時任務：

**配置參數**:
- 初始延遲: 2秒
- 執行間隔: 10分鐘
- 任務類型: 循環執行

**執行流程**:
1. 系統啟動後2秒開始首次執行
2. 每10分鐘執行一次主要業務邏輯
3. 執行時間在上午9點時觸發特殊任務
4. 每月1號執行月度清理任務

#### 11.3.2 業務任務調度
主要業務任務包括：

**常規任務** (每10分鐘):
- `Check()`: 掃描和處理PDF簽名
- `InsertECO()`: 插入ECO數據
- `ECO()`: 處理ECO業務
- `Cheque()`: 支票處理
- `State()`: 狀態更新
- `CDMSExpenseCheck()`: CDMS費用檢查
- `CDMSReceiptCheck()`: CDMS收據檢查

**特殊時間任務**:
- 上午9點: `PayslipAlertA8()`, `CertAlertA8()`
- 上午10點: `Bond()` 債券處理
- 每月1號: `ClosingClaims()`, `Updateflogseq()`

### 11.4 外部系統集成

#### 11.4.1 A8系統集成
**API端點**:
- 基礎URL: `https://i.3311csci.com/seeyon/rest/`
- 令牌獲取: `/token/common-rest-user/`
- 流程查詢: `/flow/FromFinish/`

**集成功能**:
- 用戶令牌獲取和管理
- 工作流程狀態查詢
- 表單數據提交
- 文件上傳和下載

**表單類型支持**:
- 保險業務審批表投標 (tender)
- 保險業務審批表分判 (Subcontract)
- 賠款審批表 (Indemnity)
- 聘用審批表 (Hire)
- 賠款審批表附頁 (Appendix)

#### 11.4.2 郵件系統集成
**SMTP配置**:
- 服務器: **********:587 (內部) / owahk.cohl.com:25 (外部)
- 認證: <EMAIL>
- 加密: 支持SSL/TLS

**郵件功能**:
- 客戶電子保單發送
- 工傷案例提醒
- 系統通知郵件
- 附件管理和發送

#### 11.4.3 Selenium Web自動化
**Chrome配置**:
- 瀏覽器路徑: `C:\Program Files\Google\Chrome\Application\chrome.exe`
- 驅動超時: 120秒
- 特殊參數: 靜默打印、PDF外部打開

**自動化功能**:
- 網頁PDF生成
- 表單自動填寫
- 文件自動下載
- 頁面截圖和驗證

### 11.5 錯誤處理和日誌

#### 11.5.1 NLog配置
**日誌級別**:
- Trace: 詳細跟蹤信息
- Debug: 調試信息
- Info: 一般信息
- Warn: 警告信息
- Error: 錯誤信息
- Fatal: 致命錯誤

**日誌輸出**:
- 控制台輸出
- 文件輸出
- 數據庫輸出 (可選)

#### 11.5.2 異常處理策略
**處理原則**:
- 捕獲所有可能的異常
- 記錄詳細的錯誤信息
- 實施優雅降級
- 提供錯誤恢復機制

**常見異常類型**:
- 文件訪問異常
- 數據庫連接異常
- PDF處理異常
- 網絡通信異常
- 外部API調用異常

---

**文檔結束**

*本文檔基於對現有代碼庫的深入分析生成，為PDF簽名系統的企業級需求提供了全面的技術和業務規格說明。建議在實施過程中根據實際需求進行適當調整和優化。*
